<template>
    <popup
        ref="popupRef"
        :async="true"
        :clickModalClose="false"
        title="选择机构"
        :center="true"
        @close="handleClose"
        @confirm="handleConfirm"
        width="800px"
    >
        <template #trigger>
            <slot></slot>
        </template>

        <!-- 搜索区域 -->
        <div class="mb-4">
            <el-form :model="queryParams" inline>
                <el-form-item label="机构名称">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入机构名称"
                        clearable
                        style="width: 180px"
                    />
                </el-form-item>
                <el-form-item label="省份">
                    <el-select
                        v-model="queryParams.province_id"
                        placeholder="请选择省份"
                        clearable
                        style="width: 120px"
                        @change="changeProvince"
                    >
                        <el-option
                            v-for="item in area"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="城市">
                    <el-select
                        v-model="queryParams.city_id"
                        placeholder="请选择城市"
                        clearable
                        style="width: 120px"
                    >
                        <el-option
                            v-for="item in cityOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 机构列表 -->
        <div>
            <el-table
                ref="tableRef"
                :data="pager.lists"
                size="large"
                v-loading="pager.loading"
                @selection-change="handleSelectionChange"
                max-height="400px"
            >
                <el-table-column label="选择" width="60" align="center">
                    <template #default="{ row }">
                        <el-radio
                            v-model="selectedInstitute"
                            :label="row.id"
                            @click="handleRadioChange(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column property="id" label="ID" min-width="80" />
                <el-table-column property="name" label="机构名称" min-width="200" />
                <el-table-column label="所属城市" min-width="200">
                    <template #default="scope">
                        {{ scope.row.province_name }} {{ scope.row.city_name }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="flex justify-end mt-4">
            <el-pagination
                v-model:current-page="pager.page"
                v-model:page-size="pager.size"
                :page-sizes="[5, 10, 20, 30]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pager.count"
                @size-change="getLists"
                @current-change="getLists"
            />
        </div>
    </popup>
</template>

<script lang="ts" setup name="instituteSelect">
import Popup from '@/components/popup/index.vue'
import { apiInstituteLists } from '@/api/marketing/institute'
import { reactive, ref } from 'vue'
import type { ElTable } from 'element-plus'
import { usePaging } from '@/hooks/usePaging'
import area from '@/utils/area'

const tableRef = shallowRef<InstanceType<typeof ElTable>>()
const popupRef = ref<any>(null)

// 查询参数
const queryParams = reactive({
    name: '',
    province_id: '' as string | number,
    city_id: '' as string | number
})

// 城市选项
const cityOptions = ref([]) as any

// 选中的机构
const selectedInstitute = ref<number | null>(null)
const selectedInstituteData = ref<any>(null)

// 省份变化时更新城市选项
const changeProvince = () => {
    cityOptions.value = area.find((item) => item.value == queryParams.province_id)?.children || []
    queryParams.city_id = ''
}

// 分页
const { pager, getLists, resetPage } = usePaging({
    fetchFun: apiInstituteLists,
    params: queryParams
})

// 重置搜索
const resetSearch = () => {
    queryParams.name = ''
    queryParams.province_id = ''
    queryParams.city_id = ''
    cityOptions.value = []
    resetPage()
}

// 单选框变化
const handleRadioChange = (row: any) => {
    selectedInstituteData.value = row
}

// 表格选择变化（这里用不到，但保留接口）
const handleSelectionChange = (selection: any[]) => {
    // 单选模式下不需要处理
}

// 弹窗关闭
const handleClose = () => {
    selectedInstitute.value = null
    selectedInstituteData.value = null
    queryParams.name = ''
    queryParams.province_id = ''
    queryParams.city_id = ''
    cityOptions.value = []
}

// 确认选择
const handleConfirm = () => {
    if (!selectedInstituteData.value) {
        ElMessage.warning('请选择一个机构')
        return
    }
    
    emit('confirm', selectedInstituteData.value)
    popupRef.value.visible = false
}

// 打开弹窗
const open = () => {
    popupRef.value?.open()
    getLists()
}

const emit = defineEmits(['confirm'])

defineExpose({
    open
})
</script>

<style lang="scss" scoped>
/* 单选按钮样式 */
:deep(.el-radio__label) {
    display: none;
}

/* 弹窗标题居左 */
:deep(.el-dialog__header) {
    text-align: left;
    padding: 20px 20px 10px 20px;
}

/* 弹窗底部按钮居右 */
:deep(.dialog-footer) {
    text-align: right;
    padding: 10px 0;
}

:deep(.el-dialog__footer) {
    text-align: right;
    padding: 10px 20px 20px 20px;
}

/* 弹窗内容区域 */
:deep(.el-dialog__body) {
    padding: 10px 20px;
}
</style>
