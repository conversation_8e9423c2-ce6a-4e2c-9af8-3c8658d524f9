<template>
    <el-card shadow="never" class="!border-none">
        <el-form :model="formData" inline>
            <el-form-item label="关键词">
                <el-input class="ls-input" v-model="formData.keyword" placeholder="城市运营商名称/手机号码" />
            </el-form-item>
            <el-form-item label="状态">
                <el-select class="ls-input" v-model="formData.status" placeholder="请选择状态">
                    <el-option label="全部" value="" />
                    <el-option label="启用" :value="0" />
                    <el-option label="禁用" :value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="添加时间">
                <data-picker
                    class="ls-input"
                    style="width: 280px"
                    v-model:start_time="formData.start_time"
                    v-model:end_time="formData.end_time"
                ></data-picker>
            </el-form-item>
            <el-form-item>
                <div class="flex">
                    <el-button type="primary" @click="getLists">查询</el-button>
                    <el-button @click="handleResetParams">重置</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <div class="flex justify-between mb-4">
            <el-button
                v-perms="['partner.partner/add']"
                type="primary"
                @click="handleAdd"
            >
                添加城市运营商
            </el-button>
            <div></div>
        </div>
        <div class="mt-3">
            <el-table :data="pager.lists" style="width: 100%" v-loading="pager.loading">
                <el-table-column property="id" label="ID" min-width="80" />
                <el-table-column property="name" label="名称" min-width="120" />
                <el-table-column property="mobile" label="手机号码" min-width="120" />
                <el-table-column property="account" label="登录账号" min-width="120" />
                <el-table-column label="状态" min-width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">
                            {{ scope.row.status_str }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="create_time" label="添加时间" min-width="180" />
                <el-table-column label="操作" min-width="180" fixed="right">
                    <template #default="scope">
                        <div class="flex items-center">
                            <el-button
                                v-perms="['partner.partner/edit']"
                                type="primary"
                                link
                                @click="handleEdit(scope.row.id)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['partner.partner/edit']"
                                :type="scope.row.status == 0 ? 'danger' : 'success'"
                                link
                                @click="handleStatusChange(scope.row)"
                            >
                                {{ scope.row.status == 0 ? '禁用' : '启用' }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getLists" />
        </div>
    </el-card>
</template>

<script lang="ts" setup name="partner">
import { apiPartnerLists, apiPartnerStatus } from '@/api/master_worker/partner'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import Pagination from '@/components/pagination/index.vue'
import DataPicker from '@/components/data-picker/index.vue'

const router = useRouter()

interface FormDataObj {
    keyword?: string
    status: number | string
    start_time: string
    end_time: string
}

const formData = ref<FormDataObj>({
    keyword: '',
    status: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiPartnerLists,
    params: formData.value
})

// 重置筛选框
const handleResetParams = () => {
    resetParams()
}

// 添加城市运营商
const handleAdd = () => {
    router.push('/master_worker/partner/edit')
}

// 编辑城市运营商
const handleEdit = (id: number) => {
    router.push({
        path: '/master_worker/partner/edit',
        query: { id }
    })
}

// 启用/禁用状态切换
const handleStatusChange = async (row: any) => {
    const newStatus = row.status == 0 ? 1 : 0
    const actionText = newStatus == 0 ? '启用' : '禁用'
    
    try {
        await feedback.confirm(`确认${actionText}该城市运营商吗？`)
        await apiPartnerStatus({ id: row.id, status: newStatus })
        getLists()
    } catch (error) {
        // 用户取消操作
    }
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}
</style>
