import request from '@/utils/request'

// 列表
export function apiMasterWorkerApplyLists(params: any) {
    return request.get({ url: '/staff.staff_apply/lists', params })
}

// 详情
export function apiMasterWorkerApplyDetail(params: any) {
    return request.get({ url: '/staff.staff_apply/detail', params })
}

// 审核
export function apiMasterWorkerApplyApply(params: any) {
    return request.post({ url: '/staff.staff_apply/apply', params })
}

// 城市运营商申请列表
export function apiPartnerApplyLists(params: any) {
    return request.get({ url: '/partner.partner_apply/lists', params })
}

// 城市运营商申请详情
export function apiPartnerApplyDetail(params: any) {
    return request.get({ url: '/partner.partner_apply/detail', params })
}

// 城市运营商申请审核
export function apiPartnerApplyAudit(params: any) {
    return request.post({ url: '/partner.partner_apply/audit', params })
}
