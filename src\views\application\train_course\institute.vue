<template>
    <el-card shadow="never" class="!border-none">
        <!-- 搜索区域 -->
        <div class="mb-4">
            <el-form :model="queryParams" inline>
                <el-form-item label="机构名称">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入机构名称"
                        clearable
                        style="width: 200px"
                    />
                </el-form-item>
                <el-form-item label="省份">
                    <el-select
                        v-model="queryParams.province_id"
                        placeholder="请选择省份"
                        clearable
                        style="width: 150px"
                        @change="changeProvince"
                    >
                        <el-option
                            v-for="item in provinceOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="城市">
                    <el-select
                        v-model="queryParams.city_id"
                        placeholder="请选择城市"
                        clearable
                        style="width: 150px"
                    >
                        <el-option
                            v-for="item in cityOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-4">
            <div class="inline-block">
                <instituteForm
                    v-perms="['institute.add']"
                    @refresh="getLists"
                    type="primary"
                    btnText="发布"
                />
            </div>
        </div>

        <!-- 表格 -->
        <div>
            <el-table
                ref="tableRef"
                :data="pager.lists"
                size="large"
                v-loading="pager.loading"
            >
                <el-table-column property="id" label="ID" min-width="80" />
                <el-table-column property="name" label="机构名称" min-width="200" />
                <el-table-column label="所属城市" min-width="200">
                    <template #default="scope">
                        {{ scope.row.province_name }} {{ scope.row.city_name }}
                    </template>
                </el-table-column>
                <el-table-column property="create_time" label="创建时间" min-width="180" />
                <el-table-column label="操作" min-width="150" fixed="right">
                    <template #default="scope">
                        <div class="flex items-center">
                            <instituteForm
                                v-perms="['institute.add']"
                                :id="scope.row.id"
                                type="text"
                                btnText="编辑"
                                @refresh="getLists"
                            />
                            <el-link
                                v-perms="['institute.del']"
                                class="ml-4"
                                type="danger"
                                :underline="false"
                                @click="handleDelete(scope.row.id)"
                            >
                                删除
                            </el-link>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getLists" />
        </div>
    </el-card>
</template>

<script lang="ts" setup name="instituteIndex">
import { apiInstituteLists, apiInstituteDelete } from '@/api/marketing/institute'
import { apiOpenCityFilterLists } from '@/api/setting/open_city'
import { onMounted, reactive, ref } from 'vue'
import feedback from '@/utils/feedback'
import type { ElTable } from 'element-plus'
import { usePaging } from '@/hooks/usePaging'
import instituteForm from './components/institute_form.vue'

const tableRef = shallowRef<InstanceType<typeof ElTable>>()

// 查询参数
const queryParams = reactive({
    name: '',
    province_id: '' as string | number,
    city_id: '' as string | number
})

// 省市数据
const provinceOptions = ref([]) as any
const cityOptions = ref([]) as any

// 获取省市数据
const getProvinceCity = async () => {
    try {
        const data = await apiOpenCityFilterLists()
        provinceOptions.value = data.map((item: any) => ({
            id: item.id,
            name: item.name,
            sub: item.sub || []
        }))
    } catch (error) {
        console.error('获取省市数据失败:', error)
    }
}

// 省份变化时更新城市选项
const changeProvince = () => {
    const selectedProvince = provinceOptions.value.find((item: any) => item.id == queryParams.province_id)
    cityOptions.value = selectedProvince?.sub || []
    queryParams.city_id = ''
}

// 分页
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiInstituteLists,
    params: queryParams
})

// 重置搜索
const resetSearch = () => {
    queryParams.name = ''
    queryParams.province_id = ''
    queryParams.city_id = ''
    cityOptions.value = []
    resetPage()
}

// 删除
const handleDelete = async (id: number): Promise<void> => {
    await feedback.confirm('确认删除该机构？')
    await apiInstituteDelete({ id })
    getLists()
}

onMounted(() => {
    getProvinceCity()
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}
</style>
