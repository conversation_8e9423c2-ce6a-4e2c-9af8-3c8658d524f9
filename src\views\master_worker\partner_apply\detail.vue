<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header content="城市运营商申请详情" @back="$router.back()" />
    </el-card>

    <!-- 审核拒绝状态显示 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none" v-if="formData.apply_status == 2">
        <div class="pl-[80px] pr-[100px]">
            <span class="font-bold text-[18px]">审核拒绝</span>
            <span class="text-[#f63737] ml-[30px]">原因：{{ formData.remarks }}</span>
        </div>
    </el-card>

    <!-- 基本信息 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="pl-[80px] pr-[100px]">
            <span class="font-bold text-[18px]">基本信息</span>
            <div class="mt-[20px]">
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">用户ID：</div>
                    <div>{{ formData.user_id }}</div>
                </div>
                <!-- <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">头像：</div>
                    <div>
                        <el-image
                            v-if="formData.avatar"
                            style="width: 80px; height: 80px"
                            :src="formData.avatar"
                            :preview-src-list="[formData.avatar]"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            :fit="'cover'"
                        />
                        <span v-else class="text-gray-400">暂无头像</span>
                    </div>
                </div>
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">昵称：</div>
                    <div>{{ formData.nickname || '-' }}</div>
                </div> -->
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">手机号码：</div>
                    <div>{{ formData.mobile || '-' }}</div>
                </div>
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">申请城市运营商区域：</div>
                    <div>{{ formData.area_str || '-' }}</div>
                </div>
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">申请状态：</div>
                    <div>
                        <el-tag 
                            :type="formData.apply_status == 1 ? 'success' : formData.apply_status == 2 ? 'danger' : 'warning'"
                        >
                            {{ formData.apply_status_str }}
                        </el-tag>
                    </div>
                </div>
                <div class="flex items-center mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px]">申请时间：</div>
                    <div>{{ formData.create_time || '-' }}</div>
                </div>
                <div class="flex items-center mb-[20px]" v-if="formData.update_time">
                    <div class="w-[120px] text-right mr-[20px]">审核时间：</div>
                    <div>{{ formData.update_time }}</div>
                </div>
            </div>
        </div>
    </el-card>

    <!-- 申请材料 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none" v-if="formData.apply_materials">
        <div class="pl-[80px] pr-[100px]">
            <span class="font-bold text-[18px]">申请材料</span>
            <div class="mt-[20px]">
                <div class="flex items-start mb-[20px]">
                    <div class="w-[120px] text-right mr-[20px] mt-[5px]">申请说明：</div>
                    <div class="flex-1">{{ formData.apply_materials.description || '-' }}</div>
                </div>
                <div class="flex items-start mb-[20px]" v-if="formData.apply_materials.images && formData.apply_materials.images.length">
                    <div class="w-[120px] text-right mr-[20px] mt-[5px]">相关图片：</div>
                    <div class="flex-1">
                        <div class="flex flex-wrap gap-2">
                            <el-image
                                v-for="(image, index) in formData.apply_materials.images"
                                :key="index"
                                style="width: 100px; height: 100px"
                                :src="image"
                                :preview-src-list="formData.apply_materials.images"
                                :initial-index="index"
                                :hide-on-click-modal="true"
                                :preview-teleported="true"
                                :fit="'cover'"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </el-card>

    <!-- 操作按钮 -->
    <footer-btns v-if="formData.apply_status == 0">
        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
    </footer-btns>

    <!-- 驳回弹窗 -->
    <el-dialog v-model="rejectDialogVisible" title="驳回申请" width="400px">
        <el-form :model="rejectForm" label-width="80px">
            <el-form-item label="驳回原因" required>
                <el-input
                    v-model="rejectForm.remarks"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入驳回原因"
                    maxlength="200"
                    show-word-limit
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="rejectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmReject">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { apiPartnerApplyDetail, apiPartnerApplyAudit } from '@/api/master_worker/apply'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FooterBtns from '@/components/footer-btns/index.vue'
import feedback from '@/utils/feedback'

const route = useRoute()
const router = useRouter()
const id: any = route.query.id

const formData = ref<any>({})
const rejectDialogVisible = ref(false)
const rejectForm = ref({
    remarks: ''
})

// 获取详情
const getDetail = async () => {
    try {
        formData.value = await apiPartnerApplyDetail({ id })
    } catch (error) {
        feedback.msgError('获取详情失败')
    }
}

// 审核通过
const handleApprove = async () => {
    try {
        await feedback.confirm('确认通过该申请吗？')
        await apiPartnerApplyAudit({ id, apply_status: 1 })
        feedback.msgSuccess('操作成功')
        router.back()
    } catch (error) {
        // 用户取消操作或操作失败
    }
}

// 驳回申请
const handleReject = () => {
    rejectForm.value.remarks = ''
    rejectDialogVisible.value = true
}

// 确认驳回
const confirmReject = async () => {
    if (!rejectForm.value.remarks.trim()) {
        feedback.msgError('请输入驳回原因')
        return
    }
    
    try {
        await apiPartnerApplyAudit({
            id,
            apply_status: 2,
            remarks: rejectForm.value.remarks
        })
        feedback.msgSuccess('操作成功')
        rejectDialogVisible.value = false
        router.back()
    } catch (error) {
        feedback.msgError('操作失败')
    }
}

onMounted(() => {
    if (id) {
        getDetail()
    } else {
        feedback.msgError('缺少必要参数')
        router.back()
    }
})
</script>

<style lang="scss" scoped>
.el-tag {
    font-size: 14px;
}
</style>
