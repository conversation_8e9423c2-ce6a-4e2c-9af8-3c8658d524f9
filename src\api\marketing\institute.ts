import request from '@/utils/request'

// 机构列表
export function apiInstituteLists(params: any) {
    return request.get({ url: '/marketing.institute/lists', params })
}

// 机构详情
export function apiInstituteDetail(params: any) {
    return request.get({ url: '/marketing.institute/detail', params })
}

// 添加/编辑机构
export function apiInstituteAdd(params: any) {
    return request.post({ url: '/marketing.institute/add', params })
}

// 删除机构
export function apiInstituteDelete(params: any) {
    return request.post({ url: '/marketing.institute/delete', params })
}
